import { Pinecone } from '@pinecone-database/pinecone';
import { OpenAI } from 'openai';
import logger from '../config/logger.js';

export class PineconeService {
  static client = null;
  static index = null;
  static openai = null;

  /**
   * Initialize Pinecone client
   * @returns {Promise<void>}
   */
  static async initialize() {
    try {
      if (!process.env.PINECONE_API_KEY) {
        logger.warn('Pinecone API key not found. Pinecone service will be disabled.');
        return;
      }

      this.client = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY,
      });

      const indexName = process.env.PINECONE_INDEX_NAME || 'theinfini-ai-chat';
      this.index = this.client.index(indexName);

      // Initialize OpenAI client for embeddings
      if (process.env.OPENAI_API_KEY) {
        this.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });
        logger.info('OpenAI client initialized for embeddings');
      } else {
        logger.warn('OpenAI API key not found. Will use fallback embedding method.');
      }

      logger.info('Pinecone service initialized successfully');
    } catch (error) {
      logger.error('Error initializing Pinecone service:', error);
      throw error;
    }
  }

  /**
   * Check if Pinecone is available
   * @returns {boolean} Whether Pinecone is available
   */
  static isAvailable() {
    return !!this.client && !!this.index;
  }

  /**
   * Generate embedding for text using OpenAI
   * @param {string} text - Text to generate embedding for
   * @returns {Promise<Array<number>>} Embedding vector
   */
  static async generateEmbedding(text) {
    try {
      if (this.openai) {
        // Use OpenAI's embedding API with dimension matching Pinecone index
        const response = await this.openai.embeddings.create({
          model: 'text-embedding-3-small',
          input: text,
          dimensions: 1536, // Match Pinecone index dimension
        });

        return response.data[0].embedding;
      } else {
        // Fallback to hash-based embedding
        const { createHash } = await import('crypto');
        const hash = createHash('sha256').update(text).digest('hex');

        // Convert hash to a 1536-dimensional vector (OpenAI embedding size)
        const vector = [];
        for (let i = 0; i < 1536; i++) {
          const charCode = hash.charCodeAt(i % hash.length);
          vector.push((charCode / 255) * 2 - 1); // Normalize to [-1, 1]
        }

        return vector;
      }
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  /**
   * Store chat message in Pinecone
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} messageId - Message ID
   * @param {string} message - User message
   * @param {string} response - Assistant response
   * @param {Object} [metadata] - Additional metadata
   * @returns {Promise<void>}
   */
  static async storeMessage(
    projectId,
    threadId,
    messageId,
    message,
    response,
    metadata = {}
  ) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping message storage');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      const combinedText = `User: ${message}\nAssistant: ${response}`;
      const embedding = await this.generateEmbedding(combinedText);

      await this.index.namespace(namespace).upsert([
        {
          id: messageId,
          values: embedding,
          metadata: {
            projectId,
            threadId,
            message,
            response,
            timestamp: new Date().toISOString(),
            ...metadata,
          },
        },
      ]);

      logger.debug(`Stored message in Pinecone: ${messageId}`);
    } catch (error) {
      logger.error('Error storing message in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Search for similar messages in project context
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} query - Search query
   * @param {number} [topK] - Number of results to return
   * @returns {Promise<Array>} Search results
   */
  static async searchSimilarMessages(
    projectId,
    threadId,
    query,
    topK = 5
  ) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty results');
        return [];
      }

      const namespace = `${projectId}_${threadId}`;
      const queryEmbedding = await this.generateEmbedding(query);

      const searchResults = await this.index.namespace(namespace).query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      });

      return searchResults.matches || [];
    } catch (error) {
      logger.error('Error searching similar messages in Pinecone:', error);
      return [];
    }
  }

  /**
   * Delete all messages for a thread
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @returns {Promise<void>}
   */
  static async deleteThreadMessages(projectId, threadId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping thread deletion');
        return;
      }

      const namespace = `${projectId}_${threadId}`;

      // Check if namespace exists before attempting deletion
      try {
        const stats = await this.index.describeIndexStats();
        const namespaceExists = stats.namespaces && stats.namespaces.hasOwnProperty(namespace);

        if (!namespaceExists) {
          logger.info(`Namespace ${namespace} does not exist, skipping deletion`);
          return;
        }

        // Delete all vectors in the namespace
        await this.index.namespace(namespace).deleteAll();
        logger.info(`Deleted all messages for thread ${threadId} in project ${projectId}`);
      } catch (deleteError) {
        // Handle 404 errors gracefully - namespace might not exist or be empty
        if (deleteError.name === 'PineconeNotFoundError' || deleteError.message?.includes('404')) {
          logger.info(`Namespace ${namespace} not found or empty, skipping deletion`);
          return;
        }
        throw deleteError;
      }
    } catch (error) {
      logger.error('Error deleting thread messages from Pinecone:', error);
      // Don't throw the error to prevent blocking thread deletion in the database
      // Log the error but continue with the operation
    }
  }

  /**
   * Delete all messages for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<void>}
   */
  static async deleteProjectMessages(projectId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping project deletion');
        return;
      }

      // Note: This is a simplified implementation
      // In a real scenario, you might need to list all namespaces for the project
      // and delete them individually
      
      logger.warn(`Project deletion for ${projectId} - manual cleanup may be required`);
    } catch (error) {
      logger.error('Error deleting project messages from Pinecone:', error);
      throw error;
    }
  }

  /**
   * Get namespace statistics
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @returns {Promise<Object>} Namespace statistics
   */
  static async getNamespaceStats(projectId, threadId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty stats');
        return { vectorCount: 0 };
      }

      const namespace = `${projectId}_${threadId}`;
      const stats = await this.index.describeIndexStats();
      
      return {
        vectorCount: stats.namespaces?.[namespace]?.vectorCount || 0,
        totalVectors: stats.totalVectorCount || 0,
      };
    } catch (error) {
      logger.error('Error getting namespace stats from Pinecone:', error);
      return { vectorCount: 0 };
    }
  }

  /**
   * Update message metadata
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} messageId - Message ID
   * @param {Object} metadata - New metadata
   * @returns {Promise<void>}
   */
  static async updateMessageMetadata(projectId, threadId, messageId, metadata) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping metadata update');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      
      // Fetch existing vector
      const fetchResult = await this.index.namespace(namespace).fetch([messageId]);
      const existingVector = fetchResult.vectors?.[messageId];
      
      if (!existingVector) {
        logger.warn(`Vector ${messageId} not found for metadata update`);
        return;
      }

      // Update with new metadata
      await this.index.namespace(namespace).upsert([
        {
          id: messageId,
          values: existingVector.values,
          metadata: {
            ...existingVector.metadata,
            ...metadata,
            updatedAt: new Date().toISOString(),
          },
        },
      ]);

      logger.debug(`Updated metadata for message: ${messageId}`);
    } catch (error) {
      logger.error('Error updating message metadata in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Batch store multiple messages
   * @param {Array} messages - Array of message objects
   * @returns {Promise<void>}
   */
  static async batchStoreMessages(messages) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping batch storage');
        return;
      }

      const batchSize = 100; // Pinecone batch limit
      
      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        
        for (const msg of batch) {
          await this.storeMessage(
            msg.projectId,
            msg.threadId,
            msg.messageId,
            msg.message,
            msg.response,
            msg.metadata
          );
        }
      }

      logger.info(`Batch stored ${messages.length} messages in Pinecone`);
    } catch (error) {
      logger.error('Error batch storing messages in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Store file content in Pinecone with session-based namespace
   * @param {string} content - File content to store
   * @param {Object} metadata - File metadata including sessionId
   * @returns {Promise<Object>} Result object with fileId and lastLSN for freshness checking
   */
  static async storeFileContent(content, metadata = {}) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping file content storage');
        return { fileId: null, lastLSN: null };
      }

      // Validate content
      if (!content || typeof content !== 'string' || content.trim().length === 0) {
        logger.warn('Invalid or empty content provided to storeFileContent');
        return { fileId: null, lastLSN: null };
      }

      const { sessionId, fileName } = metadata;
      if (!sessionId) {
        logger.warn('No sessionId provided for Pinecone storage');
        return { fileId: null, lastLSN: null };
      }

      // Generate unique file ID
      const fileId = `${Date.now()}_${fileName?.replace(/[^a-zA-Z0-9.-]/g, '_') || 'unknown'}`;

      // Split content into chunks for better semantic search
      const chunks = this.splitTextIntoChunks(content, 1000); // 1000 char chunks
      const namespace = sessionId;
      let lastLSN = null;

      logger.debug(`[Pinecone] Starting to store ${chunks.length} chunks for file ${fileId} in namespace ${namespace}`);

      // Note: Pinecone automatically creates namespaces if they don't exist during upsert
      for (let i = 0; i < chunks.length; i++) {
        const chunkId = `${fileId}_chunk_${i}`;
        const embedding = await this.generateEmbedding(chunks[i]);

        const upsertResult = await this.index.namespace(namespace).upsert([
          {
            id: chunkId,
            values: embedding,
            metadata: {
              fileId,
              chunkIndex: i,
              content: chunks[i],
              originalFileName: metadata.fileName || metadata.originalFileName,
              mimeType: metadata.fileType || metadata.mimeType,
              timestamp: new Date().toISOString(),
              userId: metadata.userId,
              s3Key: metadata.s3Key,
              processingMethod: metadata.processingMethod || 'standard',
            },
          },
        ]);

        // Capture the LSN from the last upsert for freshness checking
        if (upsertResult && upsertResult.usage) {
          lastLSN = upsertResult.usage.writeUnits || lastLSN;
        }
      }

      logger.info(`[Pinecone] Stored ${chunks.length} chunks for file ${fileId} in namespace ${namespace}`);

      // Return information for freshness checking
      return {
        fileId,
        lastLSN,
        namespace,
        chunkCount: chunks.length
      };
    } catch (error) {
      logger.error('Error storing file content in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Check if a namespace exists for a given session ID
   * @param {string} sessionId - Session ID for namespace
   * @param {boolean} [withRetry=false] - Whether to retry with delay for newly created namespaces
   * @returns {Promise<boolean>} Whether the namespace exists and has content
   */
  static async namespaceExists(sessionId, withRetry = false) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning false for namespace existence check');
        return false;
      }

      const namespace = sessionId;
      const maxRetries = withRetry ? 5 : 2; // Increased retries for better reliability
      const baseDelay = 1000; // Start with 1 second

      logger.debug(`[Pinecone] Starting namespace existence check for: ${namespace} (withRetry: ${withRetry})`);

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        // First, try the stats-based approach
        const statsResult = await this.checkNamespaceInStats(namespace, attempt, maxRetries);

        if (statsResult.exists && statsResult.hasVectors) {
          logger.debug(`[Pinecone] ✅ Namespace ${namespace} confirmed via stats with ${statsResult.vectorCount} vectors`);
          return true;
        }

        // If stats show namespace exists but no vectors, or if stats are unreliable,
        // try a direct query approach for better accuracy
        if (statsResult.exists || attempt > 1) {
          logger.debug(`[Pinecone] 🔍 Attempting direct query verification for namespace ${namespace}`);
          const queryResult = await this.verifyNamespaceWithQuery(namespace);

          if (queryResult) {
            logger.debug(`[Pinecone] ✅ Namespace ${namespace} confirmed via direct query`);
            return true;
          }
        }

        // If this is the last attempt, return false
        if (attempt >= maxRetries) {
          logger.debug(`[Pinecone] ❌ Namespace ${namespace} not found after ${maxRetries} attempts`);
          return false;
        }

        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 500;
        logger.debug(`[Pinecone] Waiting ${Math.round(delay)}ms before retry ${attempt + 1}/${maxRetries}...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      return false;
    } catch (error) {
      logger.error('Error checking namespace existence in Pinecone:', error);
      return false;
    }
  }

  /**
   * Check namespace existence using describeIndexStats
   * @param {string} namespace - Namespace to check
   * @param {number} attempt - Current attempt number
   * @param {number} maxRetries - Maximum retry attempts
   * @returns {Promise<Object>} Result object with existence and vector info
   */
  static async checkNamespaceInStats(namespace, attempt, maxRetries) {
    try {
      const stats = await this.index.describeIndexStats();

      // Log all available namespaces for debugging
      const allNamespaces = stats.namespaces ? Object.keys(stats.namespaces) : [];
      logger.debug(`[Pinecone] Stats check for namespace: ${namespace} (attempt ${attempt}/${maxRetries})`);
      logger.debug(`[Pinecone] Available namespaces in stats: [${allNamespaces.join(', ')}]`);

      // Check if namespaces object exists
      if (!stats.namespaces) {
        logger.debug(`[Pinecone] No namespaces found in index stats`);
        return { exists: false, hasVectors: false, vectorCount: 0 };
      }

      // Check if our specific namespace exists
      const namespaceExists = stats.namespaces.hasOwnProperty(namespace);

      if (namespaceExists) {
        const vectorCount = stats.namespaces[namespace].vectorCount || 0;
        logger.debug(`[Pinecone] Namespace ${namespace} found in stats with ${vectorCount} vectors`);

        // Note: Due to eventual consistency, vectorCount might be 0 even when vectors exist
        return {
          exists: true,
          hasVectors: vectorCount > 0,
          vectorCount: vectorCount
        };
      } else {
        logger.debug(`[Pinecone] Namespace ${namespace} not found in stats`);
        return { exists: false, hasVectors: false, vectorCount: 0 };
      }
    } catch (error) {
      logger.error('Error checking namespace in stats:', error);
      return { exists: false, hasVectors: false, vectorCount: 0 };
    }
  }

  /**
   * Verify namespace existence by attempting a direct query
   * This is more reliable than stats for checking actual data availability
   * @param {string} namespace - Namespace to verify
   * @returns {Promise<boolean>} Whether the namespace has queryable content
   */
  static async verifyNamespaceWithQuery(namespace) {
    try {
      // Try a simple query to see if the namespace responds
      // We'll use a dummy vector for the query
      const dummyVector = new Array(1536).fill(0.1); // Standard embedding dimension

      const queryResult = await this.index.namespace(namespace).query({
        vector: dummyVector,
        topK: 1,
        includeMetadata: false,
        includeValues: false
      });

      // If we get any matches, the namespace definitely exists and has content
      if (queryResult.matches && queryResult.matches.length > 0) {
        logger.debug(`[Pinecone] Direct query found ${queryResult.matches.length} vectors in namespace ${namespace}`);
        return true;
      }

      // If no matches but no error, namespace might exist but be empty or have different dimensions
      logger.debug(`[Pinecone] Direct query returned no matches for namespace ${namespace}`);
      return false;
    } catch (error) {
      // If we get a specific "namespace not found" error, it definitely doesn't exist
      if (error.message && error.message.includes('namespace') && error.message.includes('not found')) {
        logger.debug(`[Pinecone] Direct query confirmed namespace ${namespace} does not exist`);
        return false;
      }

      // For other errors (like dimension mismatch), we can't be sure
      logger.debug(`[Pinecone] Direct query error for namespace ${namespace}: ${error.message}`);
      return false;
    }
  }

  /**
   * Wait for data freshness using LSN (Log Sequence Number) comparison
   * This is the most reliable way to ensure data is available after upsert
   * @param {string} namespace - Namespace to check
   * @param {string} upsertLSN - LSN from the upsert operation
   * @param {number} [maxWaitMs=30000] - Maximum time to wait in milliseconds
   * @returns {Promise<boolean>} Whether data is fresh and available
   */
  static async waitForDataFreshness(namespace, upsertLSN, maxWaitMs = 30000) {
    if (!upsertLSN) {
      logger.debug('[Pinecone] No upsert LSN provided, skipping freshness check');
      return true;
    }

    const startTime = Date.now();
    const checkInterval = 1000; // Check every 1 second

    logger.debug(`[Pinecone] Waiting for data freshness in namespace ${namespace} with LSN ${upsertLSN}`);

    while (Date.now() - startTime < maxWaitMs) {
      try {
        // Perform a simple query to get the current LSN
        const dummyVector = new Array(1536).fill(0.1);
        const queryResult = await this.index.namespace(namespace).query({
          vector: dummyVector,
          topK: 1,
          includeMetadata: false,
          includeValues: false
        });

        // Check if the query LSN is >= upsert LSN
        if (queryResult.usage && queryResult.usage.readUnits) {
          // If we can query successfully, data is likely fresh
          logger.debug(`[Pinecone] Data appears fresh in namespace ${namespace}`);
          return true;
        }

        await new Promise(resolve => setTimeout(resolve, checkInterval));
      } catch (error) {
        logger.debug(`[Pinecone] Error during freshness check: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }

    logger.warn(`[Pinecone] Data freshness timeout after ${maxWaitMs}ms for namespace ${namespace}`);
    return false;
  }

  /**
   * Enhanced namespace existence check with freshness verification
   * @param {string} sessionId - Session ID for namespace
   * @param {string} [recentUpsertLSN] - LSN from recent upsert operation
   * @param {boolean} [withRetry=false] - Whether to retry with delay
   * @returns {Promise<boolean>} Whether the namespace exists and has fresh content
   */
  static async namespaceExistsWithFreshness(sessionId, recentUpsertLSN = null, withRetry = false) {
    try {
      // First check basic existence
      const exists = await this.namespaceExists(sessionId, withRetry);

      if (!exists) {
        return false;
      }

      // If we have a recent upsert LSN, wait for freshness
      if (recentUpsertLSN) {
        const isFresh = await this.waitForDataFreshness(sessionId, recentUpsertLSN);
        if (!isFresh) {
          logger.warn(`[Pinecone] Namespace ${sessionId} exists but data may not be fresh`);
        }
        return isFresh;
      }

      return true;
    } catch (error) {
      logger.error('Error checking namespace existence with freshness:', error);
      return false;
    }
  }

  /**
   * Search for relevant content based on user query
   * @param {string} sessionId - Session ID for namespace
   * @param {string} query - User query
   * @param {number} [topK] - Number of results to return
   * @returns {Promise<Array>} Relevant content chunks
   */
  static async searchRelevantContent(sessionId, query, topK = 5) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty results');
        return [];
      }

      const namespace = sessionId;
      const queryEmbedding = await this.generateEmbedding(query);

      logger.debug(`[Pinecone] Searching in namespace: ${namespace} with query: "${query.substring(0, 50)}..."`);

      const searchResults = await this.index.namespace(namespace).query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      });

      logger.debug(`[Pinecone] Found ${searchResults.matches?.length || 0} matches in namespace ${namespace}`);

      // Log all match scores for debugging
      if (searchResults.matches && searchResults.matches.length > 0) {
        const scores = searchResults.matches.map(match => match.score);
        logger.debug(`[Pinecone] Match scores: ${scores.join(', ')}`);
      }

      // Use a lower similarity threshold for better recall (0.2 instead of 0.5)
      // In practice, even scores around 0.3-0.4 can be quite relevant
      const similarityThreshold = 0.2;

      const relevantContent = searchResults.matches
        .filter(match => match.score > similarityThreshold)
        .map(match => ({
          content: match.metadata.content,
          score: match.score,
          fileName: match.metadata.originalFileName,
          chunkIndex: match.metadata.chunkIndex,
        }));

      logger.debug(`Found ${relevantContent.length} relevant content chunks for query in namespace ${namespace} (threshold: ${similarityThreshold})`);
      return relevantContent;
    } catch (error) {
      logger.error('Error searching relevant content in Pinecone:', error);
      return [];
    }
  }

  /**
   * Split text into chunks for better embedding storage
   * @param {string} text - Text to split
   * @param {number} maxChunkSize - Maximum chunk size in characters
   * @returns {Array<string>} Text chunks
   */
  static splitTextIntoChunks(text, maxChunkSize = 1000) {
    // Validate input
    if (!text || typeof text !== 'string') {
      logger.warn('Invalid text provided to splitTextIntoChunks');
      return [];
    }

    const chunks = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    let currentChunk = '';

    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      if (currentChunk.length + trimmedSentence.length + 1 <= maxChunkSize) {
        currentChunk += (currentChunk ? '. ' : '') + trimmedSentence;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk + '.');
        }
        currentChunk = trimmedSentence;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk + '.');
    }

    // If no sentences found, split by character limit
    if (chunks.length === 0 && text.length > 0) {
      for (let i = 0; i < text.length; i += maxChunkSize) {
        chunks.push(text.substring(i, i + maxChunkSize));
      }
    }

    return chunks;
  }

  /**
   * List all namespaces in the index (for debugging)
   * @returns {Promise<Array<string>>} List of namespace names
   */
  static async listNamespaces() {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty namespace list');
        return [];
      }

      const stats = await this.index.describeIndexStats();
      const namespaces = stats.namespaces ? Object.keys(stats.namespaces) : [];

      logger.debug(`Found ${namespaces.length} namespaces: ${namespaces.join(', ')}`);
      return namespaces;
    } catch (error) {
      logger.error('Error listing namespaces from Pinecone:', error);
      return [];
    }
  }

  /**
   * Get detailed namespace information (for debugging)
   * @param {string} sessionId - Session ID for namespace
   * @returns {Promise<Object>} Namespace details
   */
  static async getNamespaceDetails(sessionId) {
    try {
      if (!this.isAvailable()) {
        return { exists: false, error: 'Pinecone not available' };
      }

      const namespace = sessionId;
      const stats = await this.index.describeIndexStats();

      if (!stats.namespaces) {
        return {
          exists: false,
          reason: 'No namespaces in index',
          totalVectors: stats.totalVectorCount || 0,
          indexStats: stats
        };
      }

      const exists = stats.namespaces.hasOwnProperty(namespace);

      if (exists) {
        const vectorCount = stats.namespaces[namespace].vectorCount || 0;

        // Also try a direct query to verify
        let queryVerification = null;
        try {
          queryVerification = await this.verifyNamespaceWithQuery(namespace);
        } catch (queryError) {
          queryVerification = { error: queryError.message };
        }

        return {
          exists: true,
          vectorCount: vectorCount,
          namespace: namespace,
          allNamespaces: Object.keys(stats.namespaces),
          totalVectors: stats.totalVectorCount || 0,
          queryVerification: queryVerification,
          indexStats: stats
        };
      } else {
        return {
          exists: false,
          namespace: namespace,
          allNamespaces: Object.keys(stats.namespaces),
          totalVectors: stats.totalVectorCount || 0,
          reason: 'Namespace not found in stats',
          indexStats: stats
        };
      }
    } catch (error) {
      logger.error('Error getting namespace details from Pinecone:', error);
      return { exists: false, error: error.message };
    }
  }

  /**
   * Debug namespace issues - comprehensive check
   * @param {string} sessionId - Session ID for namespace
   * @returns {Promise<Object>} Comprehensive debug information
   */
  static async debugNamespace(sessionId) {
    try {
      logger.info(`[Pinecone Debug] Starting comprehensive namespace debug for: ${sessionId}`);

      const results = {
        sessionId,
        timestamp: new Date().toISOString(),
        pineconeAvailable: this.isAvailable(),
        checks: {}
      };

      if (!this.isAvailable()) {
        results.error = 'Pinecone service not available';
        return results;
      }

      // 1. Basic stats check
      results.checks.statsCheck = await this.checkNamespaceInStats(sessionId, 1, 1);

      // 2. Direct query check
      results.checks.queryCheck = await this.verifyNamespaceWithQuery(sessionId);

      // 3. Full namespace details
      results.checks.detailsCheck = await this.getNamespaceDetails(sessionId);

      // 4. List all namespaces
      results.checks.allNamespaces = await this.listNamespaces();

      // 5. Index health
      results.checks.healthCheck = await this.getHealthStatus();

      logger.info(`[Pinecone Debug] Debug complete for ${sessionId}:`, JSON.stringify(results, null, 2));
      return results;
    } catch (error) {
      logger.error('[Pinecone Debug] Error during namespace debug:', error);
      return {
        sessionId,
        timestamp: new Date().toISOString(),
        error: error.message,
        stack: error.stack
      };
    }
  }

  /**
   * Get Pinecone service health status
   * @returns {Promise<Object>} Health status
   */
  static async getHealthStatus() {
    try {
      if (!this.isAvailable()) {
        return {
          status: 'unavailable',
          message: 'Pinecone client not initialized',
        };
      }

      const stats = await this.index.describeIndexStats();

      return {
        status: 'healthy',
        totalVectors: stats.totalVectorCount || 0,
        dimension: stats.dimension || 0,
        namespaces: Object.keys(stats.namespaces || {}),
      };
    } catch (error) {
      logger.error('Error checking Pinecone health:', error);
      return {
        status: 'error',
        message: error.message,
      };
    }
  }
}
