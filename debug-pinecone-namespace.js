#!/usr/bin/env node

/**
 * Debug script for Pinecone namespace issues
 * Usage: node debug-pinecone-namespace.js <sessionId>
 */

const PineconeService = require('./src/services/PineconeService');
const logger = require('./src/utils/logger');

async function debugNamespace(sessionId) {
  try {
    console.log(`\n🔍 Starting Pinecone namespace debug for: ${sessionId}\n`);

    // Initialize Pinecone service
    await PineconeService.initialize();
    
    if (!PineconeService.isAvailable()) {
      console.error('❌ Pinecone service is not available');
      return;
    }

    console.log('✅ Pinecone service is available\n');

    // Run comprehensive debug
    const debugResults = await PineconeService.debugNamespace(sessionId);
    
    console.log('📊 Debug Results:');
    console.log('================');
    console.log(JSON.stringify(debugResults, null, 2));

    // Test the improved namespace existence check
    console.log('\n🧪 Testing improved namespace existence check...');
    
    console.log('  - Without retry:');
    const existsWithoutRetry = await PineconeService.namespaceExists(sessionId, false);
    console.log(`    Result: ${existsWithoutRetry ? '✅ EXISTS' : '❌ NOT FOUND'}`);
    
    console.log('  - With retry:');
    const existsWithRetry = await PineconeService.namespaceExists(sessionId, true);
    console.log(`    Result: ${existsWithRetry ? '✅ EXISTS' : '❌ NOT FOUND'}`);

    // Test direct query verification
    console.log('\n🎯 Testing direct query verification...');
    const queryResult = await PineconeService.verifyNamespaceWithQuery(sessionId);
    console.log(`  Result: ${queryResult ? '✅ QUERYABLE' : '❌ NOT QUERYABLE'}`);

    // Show recommendations
    console.log('\n💡 Recommendations:');
    console.log('==================');
    
    if (debugResults.checks.statsCheck.exists && !debugResults.checks.statsCheck.hasVectors) {
      console.log('⚠️  Namespace exists in stats but shows 0 vectors - this is likely due to eventual consistency');
      console.log('   → Try waiting a few seconds and check again');
      console.log('   → Use the direct query method for more reliable results');
    }
    
    if (!debugResults.checks.statsCheck.exists && debugResults.checks.queryCheck) {
      console.log('⚠️  Namespace not in stats but queryable - stats may be delayed');
      console.log('   → This is normal for recently created namespaces');
    }
    
    if (!debugResults.checks.statsCheck.exists && !debugResults.checks.queryCheck) {
      console.log('❌ Namespace truly does not exist or has no content');
      console.log('   → Check if files were uploaded correctly');
      console.log('   → Verify the session ID is correct');
    }

    console.log('\n✅ Debug complete!');

  } catch (error) {
    console.error('❌ Error during debug:', error);
    console.error(error.stack);
  }
}

// Get session ID from command line arguments
const sessionId = process.argv[2];

if (!sessionId) {
  console.error('Usage: node debug-pinecone-namespace.js <sessionId>');
  console.error('Example: node debug-pinecone-namespace.js 0ed5ef2dde6ff4e435c1d13f7326e0f6514a128d183f46c42522bba35fb6fd51');
  process.exit(1);
}

// Run the debug
debugNamespace(sessionId).then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
